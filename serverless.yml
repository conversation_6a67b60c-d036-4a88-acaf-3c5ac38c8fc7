service: eyecue-things-api
useDotenv: true

plugins:
  - serverless-better-credentials
  - serverless-python-requirements

params:
  default:
    thingsShadowTableName: ${env:THINGS_SHADOW_TABLE_NAME, 'eyecue-things-shadow'}
    useCasesTableName: ${env:USE_CASES_TABLE_NAME, 'eyecue-use-cases'}
    useCaseInstallationsTableName: ${env:USE_CASE_INSTALLATIONS_TABLE_NAME, 'eyecue-use-case-installations'}
    powertoolsServiceName: eyecue-things-api
    powertoolsLogLevel: INFO
  dev:
    powertoolsLogLevel: DEBUG

provider:
  name: aws
  runtime: python3.12
  region: ${env:AWS_REGION, 'ap-southeast-2'}
  deploymentMethod: direct
  logRetentionInDays: 365
  versionFunctions: false
  stage: ${env:ENVIRONMENT, 'dev'}
  stackTags: &stackTags
    Environment: ${env:ENVIRONMENT, 'dev'}
    Product: Eyecue
    Customer: ${env:ORGANIZATION}
    Terraform: "False"
    Stack: CV
    Serverless: "True"
    Application: ${self:service}
    Squad: Core
    Customer Facing: "False"
    System: ${self:provider.runtime}
  tags: *stackTags
  environment:
    THINGS_SHADOW_TABLE_NAME: ${param:thingsShadowTableName}
    POWERTOOLS_SERVICE_NAME: ${param:powertoolsServiceName}
    POWERTOOLS_LOG_LEVEL: ${param:powertoolsLogLevel}
  deploymentBucket:
    blockPublicAccess: true

  iam:
    role:
      tags: *stackTags
      statements:
        - Effect: "Allow"
          Action:
            - "dynamodb:Scan"
            - "dynamodb:Query"
            - "dynamodb:PutItem"
            - "dynamodb:GetItem"
            - "dynamodb:BatchGetItem"
            - "dynamodb:UpdateItem"
            - "dynamodb:DeleteItem"
            - "dynamodb:DescribeTable"
          Resource:
            - "arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${param:thingsShadowTableName}"
            - "arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${param:useCasesTableName}"
            - "arn:aws:dynamodb:${aws:region}:${aws:accountId}:table/${param:useCaseInstallationsTableName}"

  apiGateway:
    apiKeys:
      - AppTeamTestKey

resources:
  Resources:
    UseCasesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${param:useCasesTableName}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: use_case_id
            AttributeType: S
        KeySchema:
          - AttributeName: use_case_id
            KeyType: HASH
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true

    UseCaseInstallationsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${param:useCaseInstallationsTableName}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: site_id
            AttributeType: S
          - AttributeName: installation_id
            AttributeType: S
        KeySchema:
          - AttributeName: site_id
            KeyType: HASH
          - AttributeName: installation_id
            KeyType: RANGE
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true

package:
  patterns:
    - "!./**"
    - eyecue_things_api/**
    - functions/**

functions:
  ##schemas##############
  get-schema-roi:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/schemas/rois/{roi_type}
          method: GET
          private: true
          cors: true

  get-schema-roi-use-case:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/schemas/use-case-rois/{use_case_id}
          method: GET
          private: true
          cors: true

  get-schema-use-cases:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/schemas/use-cases
          method: GET
          private: true
          cors: true

  get-schema-use-case-installations:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/schemas/installations
          method: GET
          private: true
          cors: true

  ##cameras##############
  get-camera:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/sites/{site_id}/cameras/{camera_id}
          method: GET
          private: true
          cors: true

  create-camera:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/sites/{site_id}/cameras
          method: POST
          private: true
          cors: true

  update-camera:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/sites/{site_id}/cameras/{camera_id}
          method: PUT
          private: true
          cors: true

  delete-camera:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/sites/{site_id}/cameras/{camera_id}
          method: DELETE
          private: true
          cors: true

  ##rois##############
  get-roi:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/sites/{site_id}/cameras/{camera_id}/rois/{roi_id}
          method: GET
          private: true
          cors: true

  create-roi:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/sites/{site_id}/cameras/{camera_id}/rois/
          method: POST
          private: true
          cors: true

  update-roi:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/sites/{site_id}/cameras/{camera_id}/rois/{roi_id}
          method: PUT
          private: true
          cors: true

  delete-roi:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/sites/{site_id}/cameras/{camera_id}/rois/{roi_id}
          method: DELETE
          private: true
          cors: true

  ##use-cases##############
  get-use-cases:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/use-cases
          method: GET
          private: true
          cors: true

  create-use-case:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/use-cases
          method: POST
          private: true
          cors: true

  update-use-case:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/use-cases/{use_case_id}
          method: PUT
          private: true
          cors: true

  delete-use-case:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/use-cases/{use_case_id}
          method: DELETE
          private: true
          cors: true

  ##use-cases-installations#####
  get-all-installations:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/installations
          method: GET
          private: true
          cors: true

  get-installations-per-site:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/installations/sites/{site_id}
          method: GET
          private: true
          cors: true

  create-installation:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/installations
          method: POST
          private: true
          cors: true

  update-installation:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/installations/{installation_id}
          method: PUT
          private: true
          cors: true

  delete-installation:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /api/v1/installations/{installation_id}
          method: DELETE
          private: true
          cors: true

  # Any route not implemented will return a 404 error
  fallback-handler:
    handler: eyecue_things_api/app.lambda_handler
    events:
      - http:
          path: /{proxy+}
          method: any
          private: false
          cors: true
